{"version": "2.0.0", "tasks": [{"label": "Install Dependencies", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run FastAPI Server", "type": "shell", "command": "python", "args": ["main.py", "--reload"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Worker", "type": "shell", "command": "python", "args": ["worker.py", "--id", "task-worker-1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "python", "args": ["run_tests.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Tests with Coverage", "type": "shell", "command": "python", "args": ["-m", "pytest", "tests/", "--cov=src", "--cov-report=html"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Format Code", "type": "shell", "command": "black", "args": ["."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Lint Code", "type": "shell", "command": "flake8", "args": ["src/", "tests/", "*.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}